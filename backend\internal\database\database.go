package database

import (
	"database/sql"
	"fmt"

	_ "modernc.org/sqlite"
)

// Initialize creates and returns a database connection
func Initialize(databaseURL string) (*sql.DB, error) {
	db, err := sql.Open("sqlite", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Enable foreign keys for SQLite
	if _, err := db.Exec("PRAGMA foreign_keys = ON"); err != nil {
		return nil, fmt.Errorf("failed to enable foreign keys: %w", err)
	}

	return db, nil
}

// Migrate runs database migrations
func Migrate(db *sql.DB) error {
	migrations := []string{
		createLocationsTable,
		createSchedulesTable,
		createVisitsTable,
		createTasksTable,
		insertSampleData,
	}

	for i, migration := range migrations {
		if _, err := db.Exec(migration); err != nil {
			return fmt.Errorf("failed to run migration %d: %w", i+1, err)
		}
	}

	return nil
}

const createLocationsTable = `
CREATE TABLE IF NOT EXISTS locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    latitude REAL DEFAULT 0,
    longitude REAL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);`

const createSchedulesTable = `
CREATE TABLE IF NOT EXISTS schedules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_name TEXT NOT NULL,
    client_id INTEGER NOT NULL,
    caregiver_id INTEGER NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    location_id INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'missed')),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (location_id) REFERENCES locations(id)
);`

const createVisitsTable = `
CREATE TABLE IF NOT EXISTS visits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    schedule_id INTEGER NOT NULL UNIQUE,
    start_time DATETIME,
    end_time DATETIME,
    start_latitude REAL,
    start_longitude REAL,
    end_latitude REAL,
    end_longitude REAL,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
);`

const createTasksTable = `
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    schedule_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'not_completed')),
    reason TEXT,
    completed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
);`

const insertSampleData = `
-- Insert sample locations
INSERT OR IGNORE INTO locations (id, address, city, state, zip_code, latitude, longitude) VALUES
(1, '123 Main St', 'Springfield', 'IL', '62701', 39.7817, -89.6501),
(2, '456 Oak Ave', 'Springfield', 'IL', '62702', 39.7990, -89.6440),
(3, '789 Pine Rd', 'Springfield', 'IL', '62703', 39.7665, -89.6808);

-- Insert sample schedules
INSERT OR IGNORE INTO schedules (id, client_name, client_id, caregiver_id, start_time, end_time, location_id, status, notes) VALUES
(1, 'John Smith', 101, 1, datetime('now', '+1 hour'), datetime('now', '+3 hours'), 1, 'scheduled', 'Regular morning visit'),
(2, 'Mary Johnson', 102, 1, datetime('now', '+4 hours'), datetime('now', '+6 hours'), 2, 'scheduled', 'Afternoon medication assistance'),
(3, 'Robert Brown', 103, 1, datetime('now', '-2 hours'), datetime('now'), 3, 'missed', 'Client was not home'),
(4, 'Sarah Davis', 104, 1, datetime('now', '+1 day', '+2 hours'), datetime('now', '+1 day', '+4 hours'), 1, 'scheduled', 'Tomorrow morning visit');

-- Insert sample visits
INSERT OR IGNORE INTO visits (id, schedule_id, status) VALUES
(1, 1, 'not_started'),
(2, 2, 'not_started'),
(3, 3, 'not_started'),
(4, 4, 'not_started');

-- Insert sample tasks
INSERT OR IGNORE INTO tasks (id, schedule_id, title, description, status) VALUES
(1, 1, 'Give medication', 'Administer morning medications as prescribed', 'pending'),
(2, 1, 'Check vital signs', 'Take blood pressure and temperature', 'pending'),
(3, 1, 'Assist with bathing', 'Help client with personal hygiene', 'pending'),
(4, 2, 'Prepare lunch', 'Prepare and serve nutritious lunch', 'pending'),
(5, 2, 'Light housekeeping', 'Tidy up living areas', 'pending'),
(6, 4, 'Physical therapy exercises', 'Guide client through prescribed exercises', 'pending'),
(7, 4, 'Medication review', 'Review medication schedule with client', 'pending');`
