Objective 
This assignment evaluates your ability to deliver a clean, structured full-stack web 
application. Weʼre looking at how you handle API design, data handling, responsive UI, 
and integration of browser features like geolocation — all within a realistic healthcare 
scheduling scenario. 

Scenario 
Caregivers are assigned daily shifts to visit clients at their homes. As part of Electronic 
Visit Verification EVV compliance, each caregiver must log their visit by capturing 
timestamps and real-time location (latitude & longitude). 
Your task is to build a responsive web application (desktop + mobile) where a 
caregiver can: 
● View their schedules 
● Track todayʼs visit status: missed, upcoming, completed 
● Log Start and End of a visit with timestamp + geolocation 
● Update progress on scheduled care activities during the visit

�
�
 Home Page 
● Display simple stats: 
○ Total schedules 
○ Missed schedules 
○ Upcoming (today) 
○ Completed (today) 
● List all schedules with: 
○ Client name 
○ Shift time 
○ Location 
○ Visit status 


Schedule Details Page 
● Show full shift details, including assigned care activities 
● Provide buttons: 
○ Start Visit → capture timestamp + geolocation 
○ End Visit → capture timestamp + geolocation 
○ Visit status updates accordingly 
✅
 Task Progress Tracking During Visit) 
● Once clocked in, caregiver can see a list of care activities/tasks (e.g., “Give 
medicationˮ, “Assist with bathingˮ) 
● For each task, they can: 
○ Mark as Completed 
○ Or mark as Not Completed → must input a reason 
● Task statuses should be persisted and shown visually (e.g., checkmark or 
badge) 
✅
 Geolocation is mandatory. Use the browserʼs built-in Geolocation API. If not 
accessible, clearly indicate the fallback handling and explain in the README.


�
�
 Technical Requirements 
We prefer candidates to use: 
● Frontend: React 
● Backend: Go Golang 

API Endpoints (example suggestions): 
● GET /schedules – fetch all schedules 
● GET /schedules/today – fetch todayʼs schedules 
● GET /schedules/:id – fetch schedule details incl. tasks 
● POST /schedules/:id/start – log start time + location 
● POST /schedules/:id/end – log end time + location 
● POST /tasks/:taskId/update – update task status and optional reason 
Bonus Points: 
● Frontend: 
○ Component Reusability 
○ Use of TypeScript 
○ State Management 
● Backend: 
○ Implement the Unit Tests (any framework or library) 
○ Implement proper Logging & Error Handling 
○ Providing an API spec via Swagger UI or Postman collection