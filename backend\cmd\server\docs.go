package main

// @title Healthcare Scheduling API
// @version 1.0
// @description API for healthcare scheduling and visit management system. This API allows caregivers to manage their schedules, track visits, and update task progress.
// @termsOfService http://swagger.io/terms/

// @contact.name Healthcare Scheduling API Support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /

// @schemes http https

// @tag.name schedules
// @tag.description Operations related to schedule management

// @tag.name tasks
// @tag.description Operations related to task management

// @tag.name visits
// @tag.description Operations related to visit tracking
