# Caregivers Architecture Documentation Rules

## Frontend Architecture

### Technology Stack
- **Framework**: React + Expo for universal application development (web and mobile)
- **Language**: TypeScript for type safety and enhanced development experience
- **Mobile Target**: React Native through Expo for native mobile applications

### Component Architecture
- **Reusability**: Components must be designed for reusability across different parts of the application
- **Modularity**: Each component should have a single responsibility and be easily composable
- **Component Structure**: Follow the atomic design pattern (Atoms, Molecules, Organisms, Templates, Pages)
- **Shared Components**: Maintain a dedicated `components` directory for reusable UI elements

### State Management
- **Global State**: Use Redux Toolkit or Zustand for global state management
- **Local State**: Use React's built-in useState, useEffect, and custom hooks for component-level state
- **Async State**: Implement React Query or SWR for server state management and caching
- **State Structure**: Follow a normalized state structure to avoid duplication and maintain consistency

### Code Quality
- **Type Safety**: Utilize TypeScript interfaces and types for all props, state, and API responses
- **Code Organization**: Organize code in feature-based directories with clear separation of concerns
- **Testing**: Implement unit tests using Jest and React Testing Library
- **Linting**: Use ESLint and Prettier for consistent code formatting

## Backend Architecture

### Technology Stack
- **Language**: Go (Golang) for backend services
- **Framework**: Gin framework for HTTP routing and middleware
- **Database**: Use SQLite as database with proper connection management

### Testing Strategy
- **Unit Tests**: Implement comprehensive unit tests using Go's built-in testing package
- **Test Coverage**: Maintain minimum 80% test coverage for critical business logic
- **Table Driven Tests**: Follow Go idioms for test organization using table-driven tests
- **Integration Tests**: Create integration tests for API endpoints and database interactions

### Logging & Error Handling
- **Structured Logging**: Use structured logging with log levels (debug, info, warn, error)
- **Logging Library**: Implement logging using Zap or Logrus with JSON formatting
- **Error Handling**: Implement centralized error handling with proper error types and context
- **Error Propagation**: Follow Go idioms for error wrapping and propagation using `fmt.Errorf` and `errors.Is`
- **Monitoring**: Include trace IDs for request correlation and debugging

### API Design
- **RESTful Principles**: Follow RESTful API design principles for consistency
- **Middleware**: Implement authentication, authorization, and validation middleware
- **Request Validation**: Validate all incoming requests with appropriate error responses
- **Rate Limiting**: Implement rate limiting to prevent abuse

## API Documentation Requirements

### Swagger UI
- **Specification**: Provide comprehensive OpenAPI 3.0 specification
- **Auto-generation**: Use swaggo or similar tools to auto-generate Swagger documentation from code annotations
- **Endpoints**: Document all API endpoints with request/response schemas, parameters, and examples
- **Versioning**: Implement proper API versioning in the documentation

### Postman Collection
- **Export Format**: Provide complete Postman collection export file (JSON format)
- **Environments**: Include sample environment configurations for different deployment stages
- **Examples**: Include sample requests with various parameter combinations and expected responses
- **Documentation**: Add descriptions and notes for each endpoint and collection

### Documentation Standards
- **Consistency**: Maintain consistent documentation style across all endpoints
- **Examples**: Provide request/response examples for all API operations
- **Authentication**: Document authentication methods and token management clearly
- **Error Codes**: Document all possible HTTP status codes and error responses

## System Architecture

### Service Design
- **Microservices**: Design services with clear boundaries and responsibilities
- **Communication**: Use REST APIs for synchronous communication and message queues for asynchronous operations
- **Data Consistency**: Implement appropriate consistency patterns based on business requirements

### Security
- **Authentication**: Implement JWT-based authentication or OAuth2 as appropriate
- **Authorization**: Include role-based access control (RBAC) for different user types
- **Data Protection**: Ensure sensitive data is encrypted in transit and at rest

### Deployment
- **Containerization**: Use Docker for containerizing applications
- **Environment Configuration**: Implement environment-based configuration management
- **CI/CD**: Design for continuous integration and deployment pipelines

These architecture documentation rules provide a comprehensive framework for developing an Caregivers with React/Expo frontend and Golang backend, ensuring maintainability, scalability, and quality throughout the development lifecycle.